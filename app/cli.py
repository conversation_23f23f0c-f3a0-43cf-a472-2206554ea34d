import logging

import click

from app.service.aweme_analysis_service import AwemeAnalysisService

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


@click.group()
def cli():
    pass


@cli.command("test")
def test():
    service = AwemeAnalysisService()
    aa = service.get_by_id(61)
    service.analyze(aa)


if __name__ == "__main__":
    cli()
