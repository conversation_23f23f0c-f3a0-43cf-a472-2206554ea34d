from sqlmodel import Session, func, select

from app.models.domain import AwemeAnalysis


class AwemeAnalysisRepository:
    def __init__(self, session: Session):
        self.session = session

    def save(self, keyword: str, search_result: list, analysis_result: dict):
        aweme_analysis = AwemeAnalysis(
            keyword=keyword,
            search_result=search_result,
            analysis_result=analysis_result,
        )
        self.session.add(aweme_analysis)
        self.session.commit()
        self.session.refresh(aweme_analysis)
        return aweme_analysis

    def get_history_paginated(self, page: int, page_size: int):
        """
        分页获取搜索历史

        Args:
            page: 页码（从1开始）
            page_size: 每页数量

        Returns:
            tuple: (items, total_count)
        """
        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询数据，只选择需要的字段，按创建时间倒序
        statement = (
            select(
                AwemeAnalysis.id,
                AwemeAnalysis.keyword,
                AwemeAnalysis.status,
                AwemeAnalysis.created_at,
            )
            .order_by(AwemeAnalysis.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        items = self.session.exec(statement).all()

        # 查询总数
        count_statement = select(func.count(AwemeAnalysis.id))
        total_count = self.session.exec(count_statement).one()

        return items, total_count

    def get_by_id(self, analysis_id: int):
        """
        根据 ID 获取搜索记录

        Args:
            analysis_id: 搜索记录 ID

        Returns:
            AwemeAnalysis: 搜索记录对象，如果不存在则返回 None
        """
        return self.session.get(AwemeAnalysis, analysis_id)

    def update_by_id(self, aweme_id: int, **kwargs):
        """
        根据 ID 更新分析记录的指定字段

        Args:
            aweme_id: 分析记录 ID
            **kwargs: 要更新的字段和值

        Returns:
            AwemeAnalysis: 更新后的分析记录对象，如果记录不存在则返回 None
        """
        aweme_analysis = self.session.get(AwemeAnalysis, aweme_id)
        if not aweme_analysis:
            return None

        # 更新指定的字段
        for field, value in kwargs.items():
            if hasattr(aweme_analysis, field):
                setattr(aweme_analysis, field, value)

        self.session.add(aweme_analysis)
        self.session.commit()
        self.session.refresh(aweme_analysis)
        return aweme_analysis

    def delete_by_id(self, aweme_id: int) -> bool:
        """
        根据 ID 删除分析记录

        Args:
            aweme_id: 分析记录 ID

        Returns:
            bool: 删除成功返回 True，记录不存在返回 False
        """
        aweme_analysis = self.session.get(AwemeAnalysis, aweme_id)
        if not aweme_analysis:
            return False

        self.session.delete(aweme_analysis)
        self.session.commit()
        return True
