# 分析每个视频的旁白特点和视频前7秒内容特点，给出同一个话题的创作建议
import base64
import logging
import os
import tempfile
from typing import Any, Dict

import requests
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI
from openai import OpenAI

from app import TEMP_PATH
from app.agents.prompt import (
    ANALYSIS_PROMPT,
    SUMMARY_ANALYZE_RESULTS_PROMPT,
    VIRAL_ANALYSIS_PROMPT,
)
from app.utils import crop_video, doubao_audio_transcript, get_video_info

logger = logging.getLogger(__name__)

audio_transcript_client = OpenAI(
    api_key=os.getenv("AUDIO_TRANSCRIPT_API_KEY"),
    base_url=os.getenv("AUDIO_TRANSCRIPT_BASE_URL"),
)


def analysis_aweme_search_result(keyword: str, search_result: list[Dict[str, Any]]):
    analysis_results = []
    for aweme in search_result:
        analysis_results.append(
            _analyze_video(
                aweme["desc"],
                aweme["video_download_url"],
                aweme["music_download_url"],
            )
        )
    summary = _summary_analyze_results(keyword, analysis_results)
    return analysis_results, summary


def _analyze_video(desc: str, video_download_url: str, audio_download_url: str) -> str:
    """
    分析视频内容，提取旁白并分析爆火原因

    Args:
        video_download_url: 视频下载地址

    Returns:
        str: 格式化的分析结果
    """
    temp_video_path = None
    temp_cropped_path = None

    try:
        logger.info(f"开始分析视频: {video_download_url}")

        # 1. 下载视频 音频
        temp_video_path = _download_video(video_download_url)
        temp_audio_path = _download_audio(audio_download_url)

        # 2. 获取视频信息并判断是否需要裁剪
        video_info = get_video_info(temp_video_path)
        logger.info(f"视频时长: {video_info['duration']:.2f} 秒")

        # 3. 如果视频超过7秒，裁剪到前7秒
        if video_info["duration"] > 7:
            temp_cropped_path = crop_video(temp_video_path, start=0, end=7)
            analysis_video_path = temp_cropped_path
            logger.info("视频已裁剪到前7秒")
        else:
            analysis_video_path = temp_video_path
            logger.info("视频时长不超过7秒，无需裁剪")

        # 4. 提取音频旁白
        if os.getenv("USE_DOUBAO_AUDIO_TRANSCRIPT"):
            transcript = doubao_audio_transcript(temp_audio_path)["result"]["text"]
        else:
            transcript = _extract_audio_transcript(temp_audio_path)
        logger.info(f"音频转文字完成，内容长度: {len(transcript) if transcript else 0}")

        # 5. 分析视频开头特点（7s）
        video_hook_analysis = _analyze_video_hook(desc, analysis_video_path)
        logger.info("视频hook分析完成")

        # 6. hook分析结果和完整旁白分析
        voice_over_analysis = _analyze_video_voice_over(
            desc=desc, transcript=transcript, video_hook_analysis=video_hook_analysis
        )
        logger.info("hook分析结果+旁白分析完成")

        # 7. 格式化结果
        result = _format_analysis_result(
            desc, transcript, video_hook_analysis, voice_over_analysis
        )

        return result

    except Exception as e:
        logger.error(f"视频分析失败: {str(e)}")
        return f"视频分析失败: {str(e)}"

    finally:
        # 清理临时文件
        _cleanup_temp_files([temp_video_path, temp_cropped_path, temp_audio_path])


def _download_video(video_url: str) -> str:
    """
    下载视频到临时目录

    Args:
        video_url: 视频下载地址

    Returns:
        str: 下载后的视频文件路径
    """
    temp_path = None
    try:
        # 确保临时目录存在
        os.makedirs(TEMP_PATH, exist_ok=True)

        # 使用 tempfile 创建临时文件
        temp_fd, temp_path = tempfile.mkstemp(
            suffix=".mp4", prefix="video_", dir=TEMP_PATH
        )
        os.close(temp_fd)  # 关闭文件描述符，只返回路径

        logger.info(f"开始下载视频: {video_url}")
        logger.info(f"临时文件路径: {temp_path}")

        # 流式下载视频
        response = requests.get(video_url, stream=True, timeout=60)
        response.raise_for_status()

        with open(temp_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"视频下载完成: {temp_path}")
        return temp_path

    except Exception as e:
        # 如果下载失败，清理可能创建的临时文件
        if temp_path and os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except (OSError, FileNotFoundError):
                pass
        raise Exception(f"视频下载失败: {str(e)}")


def _download_audio(audio_url: str) -> str:
    temp_path = None
    try:
        # 确保临时目录存在
        os.makedirs(TEMP_PATH, exist_ok=True)

        # 使用 tempfile 创建临时文件
        temp_fd, temp_path = tempfile.mkstemp(
            suffix=".mp3", prefix="audio_", dir=TEMP_PATH
        )
        os.close(temp_fd)  # 关闭文件描述符，只返回路径

        logger.info(f"开始下载音频: {audio_url}")
        logger.info(f"临时文件路径: {temp_path}")

        # 流式下载视频
        response = requests.get(audio_url, stream=True, timeout=60)
        response.raise_for_status()

        with open(temp_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"音频下载完成: {temp_path}")
        return temp_path

    except Exception as e:
        # 如果下载失败，清理可能创建的临时文件
        if temp_path and os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except (OSError, FileNotFoundError):
                pass
        raise Exception(f"视频下载失败: {str(e)}")


def _extract_audio_transcript(temp_audio_path: str) -> str | None:
    """
    使用 OpenAI Whisper API 提取视频中的音频转文字

    Args:
        temp_audio_path: 音频文件路径

    Returns:
        Optional[str]: 转录的文字内容，如果失败返回None
    """
    try:
        logger.info("开始提取音频转文字")

        with open(temp_audio_path, "rb") as audio_file:
            transcript = audio_transcript_client.audio.transcriptions.create(
                model=os.getenv("AUDIO_TRANSCRIPT_MODEL"),
                file=audio_file,
            )

        text = transcript.text.strip()
        logger.info(f"音频转文字成功，内容: {text[:100]}...")
        return text if text else None

    except Exception as e:
        logger.warning(f"音频转文字失败: {str(e)}")
        return None


def _analyze_video_hook(desc: str, video_path: str) -> str:
    """
    分析视频Hook

    Args:
        video_path: 视频文件路径

    Returns:
        str: 视频Hook分析结果
    """
    try:
        logger.info("开始分析视频 Hook")

        # 将视频编码为base64
        with open(video_path, "rb") as video_file:
            base64_video = base64.b64encode(video_file.read()).decode("utf-8")

        # 调用模型分析
        completion = ChatOpenAI(model=os.getenv("VIDEO_MODEL"), temperature=0.5).stream(
            input=[
                HumanMessage(
                    content=[
                        {
                            "type": "video_url",
                            "video_url": {
                                "url": f"data:video/mp4;base64,{base64_video}"
                            },
                        },
                        {
                            "type": "text",
                            "text": ANALYSIS_PROMPT.format(desc=desc),
                        },
                    ]
                )
            ],
        )
        # 收集回复内容
        response_text = ""
        for chunk in completion:
            if chunk.content:
                response_text += chunk.content
            elif hasattr(chunk, "usage") and chunk.usage:
                logger.info(f"视频分析使用情况: {chunk.usage}")

        # completion = client.chat.completions.create(
        #     model=os.getenv("VIDEO_MODEL"),
        #     messages=[
        #         {
        #             "role": "user",
        #             "content": [
        #                 {
        #                     "type": "video_url",
        #                     "video_url": {
        #                         "url": f"data:video/mp4;base64,{base64_video}"
        #                     },
        #                 },
        #                 {"type": "text", "text": ANALYSIS_PROMPT},
        #             ],
        #         },
        #     ],
        #     modalities=["text"],
        #     stream=True,
        #     stream_options={"include_usage": True},
        # )

        # # 收集回复内容
        # response_text = ""
        # for chunk in completion:
        #     if chunk.choices:
        #         delta_content = chunk.choices[0].delta.content
        #         if delta_content:
        #             response_text += delta_content
        #     elif hasattr(chunk, "usage") and chunk.usage:
        #         logger.info(f"视频分析使用情况: {chunk.usage}")

        logger.info("视频Hook分析完成")
        return response_text.strip()

    except Exception as e:
        logger.error(f"视频Hook析失败: {str(e)}")
        return f"视频Hook分析失败: {str(e)}"


def _analyze_video_voice_over(
    desc: str, transcript: str | None, video_hook_analysis: str
) -> str:
    """
    根据视频Hook分析结果和旁白分析视频，综合分析

    Args:
        transcript: 视频旁白文字
        video_hook_analysis: 视频hook分析结果

    Returns:
        str: 综合分析结果
    """
    logger.info("开始综合分析")
    content = VIRAL_ANALYSIS_PROMPT.format(
        transcript=transcript if transcript else "无旁白内容或识别失败",
        video_hook_analysis=video_hook_analysis,
        desc=desc,
    )
    result = ChatOpenAI(model=os.getenv("AI_MODEL"), temperature=0.5).invoke(
        [HumanMessage(content)]
    )
    logger.info("综合分析完成")
    return result.content


def _format_analysis_result(
    desc: str,
    transcript: str | None,
    video_hook_analysis: str,
    voice_over_analysis: str,
) -> str:
    """
    格式化分析结果

    Args:
        transcript: 视频旁白文字
        video_content_analysis: 视频内容分析结果
        viral_analysis: 爆火潜力分析结果

    Returns:
        str: 格式化的完整分析结果
    """
    result = f"""
## 作品描述
{desc}

## 📝 视频旁白内容
{transcript if transcript else "无旁白内容或识别失败"}

## 🎬 视频Hook分析
{video_hook_analysis}

## 🔥 综合分析+创作建议
{voice_over_analysis}

---------


"""
    return result.strip()


def _cleanup_temp_files(file_paths: list[str | None]) -> None:
    """
    清理临时文件

    Args:
        file_paths: 需要清理的文件路径列表
    """
    for file_path in file_paths:
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"临时文件已清理: {file_path}")
            except (OSError, FileNotFoundError, PermissionError) as e:
                logger.warning(f"清理临时文件失败: {file_path}, 错误: {e}")


def _summary_analyze_results(keyword: str, results: list[str]) -> str:
    logger.info("开始汇总每个作品分析结果")
    aweme_analyze_results = ""
    for index, result in enumerate(results, 1):
        aweme_analyze_results += f"""
# 作品{index}
{result}
"""
    content = SUMMARY_ANALYZE_RESULTS_PROMPT.format(
        creation_theme=keyword, aweme_analyze_results=aweme_analyze_results
    )
    result = ChatOpenAI(model=os.getenv("AI_MODEL"), temperature=0.5).invoke(
        [HumanMessage(content)]
    )
    logger.info("综合分析完成")
    return result.content
