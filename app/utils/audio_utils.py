import base64
import os
import uuid

import requests


# recognize_task 函数
def doubao_audio_transcript(file_path=None):
    recognize_url = (
        "https://openspeech.bytedance.com/api/v3/auc/bigmodel/recognize/flash"
    )
    # 填入控制台获取的app id和access token
    appid = os.getenv("DOUBAO_AUDIO_TRANSCRIPT_APPID")
    token = os.getenv("DOUBAO_AUDIO_TRANSCRIPT_TOKEN")

    headers = {
        "X-Api-App-Key": appid,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": "volc.bigasr.auc_turbo",
        "X-Api-Request-Id": str(uuid.uuid4()),
        "X-Api-Sequence": "-1",
    }

    # 检查是使用文件URL还是直接上传数据
    with open(file_path, "rb") as file:
        file_data = file.read()  # 读取文件内容
        base64_data = base64.b64encode(file_data).decode("utf-8")
    audio_data = {"data": base64_data}

    request = {
        "user": {"uid": appid},
        "audio": audio_data,
        "request": {
            "model_name": "bigmodel",
            # "enable_itn": True,
            # "enable_punc": True,
            # "enable_ddc": True,
            # "enable_speaker_info": False,
        },
    }
    response = requests.post(recognize_url, json=request, headers=headers)
    code = response.headers["X-Api-Status-Code"]
    if code == "20000000":
        return response.json()
    elif code != "20000001" and code != "20000002":
        return None
