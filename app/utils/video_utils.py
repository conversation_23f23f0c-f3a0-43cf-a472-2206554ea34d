"""
视频裁剪工具模块
使用 MoviePy 实现多种视频裁剪功能
"""

import logging
import os
import tempfile
from typing import Union

from moviepy import VideoFileClip

from app import TEMP_PATH

logger = logging.getLogger(__name__)


def crop_video(
    video_path: str,
    start_time: Union[int, float] = 0,
    end_time: Union[int, float] | None = None,
) -> str:
    """
    按时间段裁剪视频

    Args:
        video_path: 原始视频文件路径
        start_time: 开始时间（秒），默认为0
        end_time: 结束时间（秒），如果为None则到视频结尾

    Returns:
        str: 裁剪后视频的文件路径
    """
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"视频文件不存在: {video_path}")

    temp_fd, output_path = tempfile.mkstemp(
        suffix=".mp4",
        prefix=f"cropped_video_{int(start_time)}_{int(end_time or 0)}",
        dir=TEMP_PATH,
    )

    os.close(temp_fd)

    try:
        with VideoFileClip(video_path) as video:
            duration = video.duration

            # 验证时间参数
            if start_time < 0:
                start_time = 0
            if start_time >= duration:
                raise ValueError(f"开始时间 {start_time} 超出视频时长 {duration}")

            # 如果没有指定结束时间，使用视频总时长
            if end_time is None:
                end_time = duration
            else:
                end_time = min(end_time, duration)

            if end_time <= start_time:
                raise ValueError(f"结束时间 {end_time} 必须大于开始时间 {start_time}")

            logger.info(f"裁剪时间段: {start_time:.2f}s - {end_time:.2f}s")

            # 裁剪视频
            clip = video.subclipped(start_time, end_time)

            # 保存裁剪后的视频
            logger.info(f"正在保存到: {output_path}")
            clip.write_videofile(
                output_path, codec="libx264", audio_codec="aac", logger=None
            )

        logger.info(f"视频裁剪完成: {output_path}")
        return output_path

    except Exception as e:
        # 如果出错且是临时文件，清理文件
        if output_path and os.path.exists(output_path):
            try:
                os.remove(output_path)
            except (OSError, FileNotFoundError):
                pass
        raise Exception(f"视频裁剪失败: {str(e)}")


def get_video_info(video_path: str) -> dict:
    """
    获取视频基本信息

    Args:
        video_path: 视频文件路径

    Returns:
        dict: 包含视频信息的字典
    """
    try:
        with VideoFileClip(video_path) as video:
            info = {
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None,
                "total_frames": int(video.duration * video.fps) if video.fps else 0,
            }
            return info
    except Exception as e:
        raise Exception(f"获取视频信息失败: {str(e)}")


def extract_audio(
    video_path: str,
) -> str:
    # 创建临时音频文件
    temp_fd, temp_audio_path = tempfile.mkstemp(
        suffix=".mp3", prefix="audio_", dir=TEMP_PATH
    )
    os.close(temp_fd)

    # 从视频中提取音频
    with VideoFileClip(video_path) as video:
        if video.audio is None:
            logger.info("视频无音频轨道")
            return None
        video.audio.write_audiofile(temp_audio_path, logger=None)
    return temp_audio_path
