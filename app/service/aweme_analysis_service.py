import logging
import math

from app.agents.analysis_agent import analysis_aweme_search_result
from app.config import get_dy_crawler
from app.db.database import get_session
from app.models.api import AnalysisHistoryItem, AnalysisHistoryResponse
from app.models.domain import AwemeAnalysis
from app.repository import AwemeAnalysisRepository

log = logging.getLogger(__name__)


class AwemeAnalysisService:
    def __init__(self, session=None):
        self.session = session or get_session()
        self.repository = AwemeAnalysisRepository(self.session)

    def create(self, keyword: str) -> AwemeAnalysis:
        aweme_analysis = self.repository.save(keyword, [], {})
        return aweme_analysis

    async def search_analyze(self, aweme_analysis: AwemeAnalysis):
        aweme_analysis = await self.search(aweme_analysis)
        self.analyze(aweme_analysis)

    async def search(self, aweme_analysis: AwemeAnalysis):
        # 获取爬虫实例
        crawler = get_dy_crawler()
        if crawler is None:
            raise RuntimeError("抖音爬虫未初始化，请确保应用已正确启动")
        # 执行搜索
        search_result = await crawler.search(aweme_analysis.keyword)
        aweme_analysis = self.repository.update_by_id(
            aweme_id=aweme_analysis.id, search_result=search_result
        )
        return aweme_analysis

    def analyze(self, aweme_analysis: AwemeAnalysis):
        analysis_results, summary = analysis_aweme_search_result(
            aweme_analysis.keyword, aweme_analysis.search_result
        )
        analysis_result = {
            "results": analysis_results,
            "summary": summary,
        }
        # 使用 repository 的更新方法
        self.repository.update_by_id(
            aweme_id=aweme_analysis.id, analysis_result=analysis_result, status=1
        )

    def get_analysis_history(
        self, page: int = 1, page_size: int = 10
    ) -> AnalysisHistoryResponse:
        """
        分页获取分析历史

        Args:
            page: 页码（从1开始）
            page_size: 每页数量

        Returns:
            AnalysisHistoryResponse: 分页的分析历史响应
        """
        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1:
            page_size = 10
        if page_size > 100:  # 限制最大页面大小
            page_size = 100

        # 从 repository 获取分页数据
        items, total_count = self.repository.get_history_paginated(page, page_size)

        # 计算总页数
        total_pages = math.ceil(total_count / page_size) if total_count > 0 else 0

        # 转换为响应模型
        history_items = []
        for item in items:
            history_items.append(
                AnalysisHistoryItem(
                    id=item.id,
                    keyword=item.keyword,
                    status=item.status,
                    created_at=item.created_at.isoformat(),
                )
            )

        return AnalysisHistoryResponse(
            items=history_items,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    def get_by_id(self, analysis_id: int) -> AwemeAnalysis | None:
        """
        根据 ID 获取搜索记录

        Args:
            analysis_id: 搜索记录 ID

        Returns:
            AwemeAnalysis: 搜索记录对象，如果不存在则返回 None
        """
        return self.repository.get_by_id(analysis_id)
