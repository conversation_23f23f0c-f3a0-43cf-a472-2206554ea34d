import asyncio
import logging
import sys

from playwright.async_api import <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page
from tenacity import Retry<PERSON>rror, retry, retry_if_result, stop_after_attempt, wait_fixed

from app.utils import convert_cookies

logger = logging.getLogger(__name__)


class DouYinLogin:
    def __init__(
        self,
        browser_context: BrowserContext,
        context_page: Page,
    ):
        self.browser_context = browser_context
        self.context_page = context_page
        self.scan_qrcode_time = 60

    async def begin(self):
        await self.popup_login_dialog()

        # check login state
        logger.info("[DouYinLogin.begin] login finished then check login state ...")
        try:
            await self.check_login_state()
        except RetryError:
            logger.info("[DouYinLogin.begin] login failed please confirm ...")
            sys.exit()

        # wait for redirect
        wait_redirect_seconds = 5
        logger.info(
            f"[DouYinLogin.begin] Login successful then wait for {wait_redirect_seconds} seconds redirect ..."
        )
        await asyncio.sleep(wait_redirect_seconds)

    @retry(
        stop=stop_after_attempt(600),
        wait=wait_fixed(1),
        retry=retry_if_result(lambda value: value is False),
    )
    async def check_login_state(self):
        """Check if the current login status is successful and return True otherwise return False"""
        current_cookie = await self.browser_context.cookies()
        _, cookie_dict = convert_cookies(current_cookie)

        for page in self.browser_context.pages:
            try:
                local_storage = await page.evaluate("() => window.localStorage")
                if local_storage.get("HasUserLogin", "") == "1":
                    return True
            except Exception:
                await asyncio.sleep(0.1)

        if cookie_dict.get("LOGIN_STATUS") == "1":
            return True

        return False

    async def popup_login_dialog(self):
        """If the login dialog box does not pop up automatically, we will manually click the login button"""
        dialog_selector = "xpath=//div[@id='login-panel-new']"
        try:
            # check dialog box is auto popup and wait for 10 seconds
            await self.context_page.wait_for_selector(
                dialog_selector, timeout=1000 * 10
            )
        except Exception as e:
            logger.error(
                f"[DouYinLogin.popup_login_dialog] login dialog box does not pop up automatically, error: {e}"
            )
            logger.info(
                "[DouYinLogin.popup_login_dialog] login dialog box does not pop up automatically, we will manually click the login button"
            )
            login_button_ele = self.context_page.locator("xpath=//p[text() = '登录']")
            await login_button_ele.click()
            await asyncio.sleep(0.5)

    # async def login_by_qrcode(self):
    #     logger.info(
    #         "[DouYinLogin.login_by_qrcode] Begin login douyin by qrcode..."
    #     )
    #     qrcode_img_selector = "xpath=//div[@id='animate_qrcode_container']//img"
    #     base64_qrcode_img = await find_login_qrcode(
    #         self.context_page, selector=qrcode_img_selector
    #     )
    #     if not base64_qrcode_img:
    #         logger.info(
    #             "[DouYinLogin.login_by_qrcode] login qrcode not found please confirm ..."
    #         )
    #         sys.exit()

    #     partial_show_qrcode = functools.partial(show_qrcode, base64_qrcode_img)
    #     asyncio.get_running_loop().run_in_executor(
    #         executor=None, func=partial_show_qrcode
    #     )
    #     await asyncio.sleep(2)
