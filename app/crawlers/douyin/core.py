import logging
from typing import Dict, List

from playwright.async_api import (
    async_playwright,
)
from playwright_stealth import Stealth

from app import DOUYIN_USER_DATA_DIR
from app.crawlers.douyin.client import DouYinClient
from app.crawlers.douyin.exception import DataFetchError
from app.crawlers.douyin.field import PublishTimeType
from app.crawlers.douyin.login import DouYinLogin
from app.utils import convert_cookies, extract_douyin_aweme

logger = logging.getLogger(__name__)


class DouYinCrawler:
    async def start(self, headless: bool = False) -> None:
        logger.info("[DouYinCrawler] 使用标准模式启动浏览器")
        self.playwright = await async_playwright().start()
        browser_context = await self.playwright.chromium.launch_persistent_context(
            user_data_dir=DOUYIN_USER_DATA_DIR,
            accept_downloads=True,
            headless=headless,
            viewport={"width": 1920, "height": 1080},
        )
        context_page = await browser_context.new_page()
        await Stealth().apply_stealth_async(context_page)
        await context_page.goto("https://www.douyin.com/jingxuan")
        cookie_str, cookie_dict = convert_cookies(await browser_context.cookies())  # type: ignore
        user_agent = await context_page.evaluate("navigator.userAgent")
        self.dy_client = DouYinClient(
            headers={
                "User-Agent": user_agent,
                "Cookie": cookie_str,
                "Host": "www.douyin.com",
                "Origin": "https://www.douyin.com/",
                "Referer": "https://www.douyin.com/",
                "Content-Type": "application/json;charset=UTF-8",
            },
            playwright_page=context_page,
            cookie_dict=cookie_dict,
        )
        if not await self.dy_client.pong(browser_context=browser_context):
            login_obj = DouYinLogin(
                browser_context=browser_context,
                context_page=context_page,
            )
            await login_obj.begin()
            await self.dy_client.update_cookies(browser_context=browser_context)

        logger.info("[DouYinCrawler.start] Douyin Crawler start finished ...")

    async def search(self, keyword: str) -> None:
        logger.info(f"[DouYinCrawler.search] Begin search douyin keyword: {keyword}")
        aweme_list: List[str] = []
        try:
            logger.info(f"[DouYinCrawler.search] search douyin keyword: {keyword}")
            posts_res = await self.dy_client.search_info_by_keyword(
                keyword=keyword,
                publish_time=PublishTimeType.UNLIMITED,
            )
            if posts_res.get("data") is None or posts_res.get("data") == []:
                logger.info(
                    f"[DouYinCrawler.search] search douyin keyword: {keyword}, data is empty,{posts_res.get('data')}`"
                )
        except DataFetchError:
            logger.error(
                f"[DouYinCrawler.search] search douyin keyword: {keyword} failed"
            )
        if "data" not in posts_res:
            logger.error(
                f"[DouYinCrawler.search] search douyin keyword: {keyword} failed，账号也许被风控了。"
            )
        for post_item in posts_res.get("data"):
            try:
                aweme_info: Dict = (
                    post_item.get("aweme_info")
                    or post_item.get("aweme_mix_info", {}).get("mix_items")[0]
                )
            except TypeError:
                continue

            aweme_list.append(await extract_douyin_aweme(aweme_info))
        return aweme_list

    async def close(self) -> None:
        """Close browser context"""
        await self.playwright.stop()
        logger.info("[DouYinCrawler.close] Browser context closed ...")
